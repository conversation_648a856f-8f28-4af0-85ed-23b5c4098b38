// Copyright Isto Inc.

using Isto.Core.Beings;
using Photon.Pun;
using UnityEngine;
using Zenject;

namespace Isto.Core.Photon
{
    /// <summary>
    /// Example script showing how to use the EnemyManager with PUN2EnemyFactory for networked enemy spawning
    /// </summary>
    public class PUN2EnemyManagerExample : MonoBehaviour
    {
        // UNITY HOOKUP

        [Header("PUN2 Enemy Example Settings")]
        [SerializeField] private string _enemyTypeToSpawn = "Spider";
        [SerializeField] private int _maxEnemies = 3;
        [SerializeField] private float _spawnRadius = 15f;
        [SerializeField] private bool _onlyMasterClientSpawns = true;
        [SerializeField] private bool _autoSpawnOnRoomJoin = true;


        // INJECTION

        private EnemyManager _enemyManager;
        private PlayerManager _playerManager;
        private PUN2NetworkManager _networkManager;

        [Inject]
        public void Inject(EnemyManager enemyManager,
                          PlayerManager playerManager,
                          PUN2NetworkManager networkManager)
        {
            _enemyManager = enemyManager;
            _playerManager = playerManager;
            _networkManager = networkManager;
        }


        // LIFECYCLE EVENTS

        private void Start()
        {
            RegisterEvents();
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }

        private void Update()
        {
            // Only allow input if we're in a networked session or single player
            if (!_networkManager.IsMultiplayerAvailable() ||
                (_networkManager.SessionState == PUN2NetworkManager.NetworkSessionState.InSession))
            {
                HandleInput();
            }
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            Events.Subscribe(Events.NETWORK_ROOM_JOINED, OnNetworkRoomJoined);
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribe(Events.NETWORK_ROOM_JOINED, OnNetworkRoomJoined);
        }

        private void OnNetworkRoomJoined()
        {
            if (_autoSpawnOnRoomJoin && ShouldSpawnEnemies())
            {
                // Delay spawning slightly to ensure all players are properly connected
                Invoke(nameof(SpawnInitialEnemies), 1f);
            }
        }


        // INPUT HANDLING

        private void HandleInput()
        {
            // Example: Press 'N' to spawn a networked enemy (only master client in multiplayer)
            if (Input.GetKeyDown(KeyCode.N))
            {
                if (ShouldSpawnEnemies())
                {
                    SpawnEnemyAtRandomPosition();
                }
                else
                {
                    Debug.Log("Only the master client can spawn enemies in multiplayer mode");
                }
            }

            // Example: Press 'M' to kill all enemies (only master client in multiplayer)
            if (Input.GetKeyDown(KeyCode.M))
            {
                if (ShouldSpawnEnemies())
                {
                    _enemyManager.KillAllEnemies();
                    Debug.Log("Master client killed all enemies!");
                }
                else
                {
                    Debug.Log("Only the master client can kill enemies in multiplayer mode");
                }
            }

            // Example: Press 'B' to respawn all dead enemies (only master client in multiplayer)
            if (Input.GetKeyDown(KeyCode.B))
            {
                if (ShouldSpawnEnemies())
                {
                    _enemyManager.RespawnAllEnemies();
                    Debug.Log("Master client respawned all dead enemies!");
                }
                else
                {
                    Debug.Log("Only the master client can respawn enemies in multiplayer mode");
                }
            }

            // Example: Press 'V' to make all enemies target the local player
            if (Input.GetKeyDown(KeyCode.V))
            {
                if (_playerManager.LocalPlayer != null)
                {
                    _enemyManager.SetAllEnemiesTarget(_playerManager.LocalPlayer.transform);
                    Debug.Log("All enemies now targeting the local player!");
                }
            }
        }


        // ENEMY SPAWNING METHODS

        /// <summary>
        /// Check if this client should spawn enemies (single player or master client)
        /// </summary>
        /// <returns>True if this client should spawn enemies</returns>
        private bool ShouldSpawnEnemies()
        {
            if (!_networkManager.IsMultiplayerAvailable())
            {
                return true; // Single player mode
            }

            if (_onlyMasterClientSpawns)
            {
                return PhotonNetwork.IsMasterClient;
            }

            return true; // Allow any client to spawn
        }

        /// <summary>
        /// Spawn initial enemies when joining a room
        /// </summary>
        private void SpawnInitialEnemies()
        {
            if (!ShouldSpawnEnemies())
                return;

            int enemiesToSpawn = Mathf.Min(_maxEnemies, _maxEnemies - _enemyManager.ActiveEnemyCount);

            for (int i = 0; i < enemiesToSpawn; i++)
            {
                SpawnEnemyAtRandomPosition();
            }

            Debug.Log($"PUN2: Spawned {enemiesToSpawn} initial enemies as master client");
        }

        /// <summary>
        /// Spawn an enemy at a random position around this object
        /// </summary>
        private void SpawnEnemyAtRandomPosition()
        {
            if (_enemyManager.ActiveEnemyCount >= _maxEnemies)
            {
                Debug.Log("Maximum number of enemies reached!");
                return;
            }

            // Generate a random position around this object
            Vector2 randomCircle = Random.insideUnitCircle * _spawnRadius;
            Vector3 spawnPosition = transform.position + new Vector3(randomCircle.x, 0, randomCircle.y);

            // Spawn the enemy (will be networked if using PUN2EnemyFactory)
            GameObject enemy = _enemyManager.CreateEnemy(_enemyTypeToSpawn);

            if (enemy != null)
            {
                string networkStatus = _networkManager.IsMultiplayerAvailable() ? "networked" : "local";
                Debug.Log($"Spawned {networkStatus} {_enemyTypeToSpawn} at {spawnPosition}");
            }
            else
            {
                Debug.LogError($"Failed to spawn enemy of type {_enemyTypeToSpawn}");
            }
        }


        // CONTEXT MENU METHODS FOR TESTING

        [ContextMenu("Spawn Enemy (Master Client Only)")]
        private void ContextSpawnEnemy()
        {
            if (ShouldSpawnEnemies())
            {
                SpawnEnemyAtRandomPosition();
            }
            else
            {
                Debug.Log("Only master client can spawn enemies");
            }
        }

        [ContextMenu("Kill All Enemies (Master Client Only)")]
        private void ContextKillAllEnemies()
        {
            if (ShouldSpawnEnemies())
            {
                _enemyManager.KillAllEnemies();
            }
            else
            {
                Debug.Log("Only master client can kill enemies");
            }
        }

        [ContextMenu("Show Network Status")]
        private void ShowNetworkStatus()
        {
            if (_networkManager.IsMultiplayerAvailable())
            {
                bool isMaster = PhotonNetwork.IsMasterClient;
                string roomName = PhotonNetwork.CurrentRoom?.Name ?? "Unknown";
                Debug.Log($"Network Status: In room '{roomName}', Master Client: {isMaster}");
            }
            else
            {
                Debug.Log("Network Status: Single player mode");
            }
        }


        // GUI FOR TESTING

        private void OnGUI()
        {
            if (_enemyManager == null || _networkManager == null) return;

            GUILayout.BeginArea(new Rect(10, 220, 350, 250));
            GUILayout.Label("PUN2 Enemy Manager Status:");
            GUILayout.Label($"Total Enemies: {_enemyManager.TotalEnemyCount}");
            GUILayout.Label($"Active Enemies: {_enemyManager.ActiveEnemyCount}");
            GUILayout.Label($"Dead Enemies: {_enemyManager.DeadEnemyCount}");

            GUILayout.Space(5);

            if (_networkManager.IsMultiplayerAvailable())
            {
                bool isMaster = PhotonNetwork.IsMasterClient;
                string roomName = PhotonNetwork.CurrentRoom?.Name ?? "Unknown";
                GUILayout.Label($"Room: {roomName}");
                GUILayout.Label($"Master Client: {isMaster}");

                if (_onlyMasterClientSpawns && !isMaster)
                {
                    GUILayout.Label("(Only Master Client can spawn)");
                }
            }
            else
            {
                GUILayout.Label("Mode: Single Player");
            }

            GUILayout.Space(5);
            GUILayout.Label("PUN2 Controls:");
            GUILayout.Label("N - Spawn Networked Enemy");
            GUILayout.Label("M - Kill All Enemies");
            GUILayout.Label("B - Respawn All Enemies");
            GUILayout.Label("V - Target Local Player");
            GUILayout.EndArea();
        }

#if UNITY_EDITOR
        private void OnDrawGizmosSelected()
        {
            // Draw spawn radius
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position, _spawnRadius);
        }
#endif
    }
}